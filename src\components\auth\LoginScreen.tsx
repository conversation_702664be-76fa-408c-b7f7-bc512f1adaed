import { useState } from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { GlobalThemeToggle } from '../GlobalThemeToggle';
import { Logo } from '../common';
import { LoginForm } from './LoginForm';
import { OTPForm } from './OTPForm';
import { ForgotPasswordForm } from './ForgotPasswordForm';
import { AccessRequestForm } from './AccessRequestForm';
import { SocialLoginButtons, SocialLoginDivider } from './SocialLoginButtons';
import { CaptchaComponent } from '../security/CaptchaComponent';
import { DevLoginModal } from './DevLoginModal';
import { AuthNavigationTabs } from './AuthNavigationTabs';

export interface LoginScreenProps {
  className?: string;
  onLogin?: (credentials: any) => void;
  onAccessRequest?: (request: any) => void;
  'data-testid'?: string;
}

export type LoginMode = 'login' | 'otp' | 'forgot' | 'access-request';

export function LoginScreen({
  className = '',
  onLogin,
  onAccessRequest,
  'data-testid': testId,
}: LoginScreenProps) {
  const { colors } = useThemeStore();
  const [loginMode, setLoginMode] = useState<LoginMode>('login');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [captchaVerified, setCaptchaVerified] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [forgotPasswordStep, setForgotPasswordStep] = useState<
    'method' | 'otp' | 'reset'
  >('method');
  const [showSocialLogin, setShowSocialLogin] = useState(false);
  const [showDevLogin, setShowDevLogin] = useState(false);

  const handleLogin = async (credentials: any) => {
    setLoading(true);
    setError('');

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLogin?.(credentials);
    } catch (err) {
      setError('Login failed. Please try again.');
      setShowCaptcha(true);
    } finally {
      setLoading(false);
    }
  };



  const handleOTPSend = async (_phone: string, _method: 'whatsapp' | 'sms') => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setOtpSent(true);
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (_data: {
    method: 'whatsapp' | 'email' | 'mobile';
    value: string;
  }) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setForgotPasswordStep('otp');
    } catch (err) {
      setError('Failed to send reset code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOTPVerify = async (_otp: string) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      onLogin?.({ method: 'otp', otp: _otp });
    } catch (err) {
      setError('Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = async (provider: string) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      onLogin?.({ method: 'social', provider });
    } catch (err) {
      setError(`Failed to login with ${provider}. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  const handleAccessRequest = async (data: any) => {
    setLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      onAccessRequest?.(data);
    } catch (err) {
      setError('Failed to submit request. Please try again.');
    } finally {
      setLoading(false);
    }
  };



  return (
    <div
      className={`min-h-screen flex items-center justify-center p-4 ${className}`}
      style={{
        background: `linear-gradient(135deg, ${colors.background} 0%, ${colors.surface} 100%)`,
      }}
      data-testid={testId}
    >
      {/* Global Theme Toggle */}
      <GlobalThemeToggle position="top-right" />

      {/* Centered Login Card */}
      <div
        className="w-full max-w-md mx-auto"
        style={{
          maxWidth: '400px', // Desktop max width
        }}
      >
        <div
          className="bg-white dark:bg-slate-800 rounded-xl shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden"
          style={{
            borderRadius: '12px',
            boxShadow: `0 20px 25px -5px ${colors.shadow}15, 0 10px 10px -5px ${colors.shadow}10`,
          }}
        >
          {/* Logo Section */}
          <div className="px-6 pt-8 pb-6 text-center">
            <Logo size="lg" className="mx-auto mb-4" />
          </div>

          {/* Form Content */}
          <div className="px-6 pb-6">
            <div className="space-y-6">
              {loginMode === 'login' && (
                <>
                  <LoginForm
                    onSubmit={handleLogin}
                    onForgotPassword={() => setLoginMode('forgot')}
                    onSwitchToOTP={() => setLoginMode('otp')}
                    loading={loading}
                    error={error}
                  />

                  {showCaptcha && !captchaVerified && (
                    <div className="mt-6">
                      <CaptchaComponent
                        onVerify={_token => {
                          setCaptchaVerified(true);
                          setShowCaptcha(false);
                        }}
                        onError={error => setError(error)}
                      />
                    </div>
                  )}

                  {/* Collapsible Social Login */}
                  <div className="mt-6">
                    <button
                      type="button"
                      onClick={() => setShowSocialLogin(!showSocialLogin)}
                      className="w-full text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors border-none bg-transparent p-2"
                    >
                      {showSocialLogin ? 'Hide' : 'Show'} social login options
                    </button>

                    {showSocialLogin && (
                      <div className="mt-4 space-y-4">
                        <SocialLoginDivider />
                        <SocialLoginButtons
                          layout="grid"
                          showLabels={false}
                          loading={loading}
                          onGoogleLogin={() => handleSocialLogin('Google')}
                          onFacebookLogin={() => handleSocialLogin('Facebook')}
                          onLinkedInLogin={() => handleSocialLogin('LinkedIn')}
                          onMicrosoftLogin={() =>
                            handleSocialLogin('Microsoft')
                          }
                        />
                      </div>
                    )}
                  </div>
                </>
              )}

              {loginMode === 'otp' && (
                <OTPForm
                  onSendOTP={handleOTPSend}
                  onVerifyOTP={handleOTPVerify}
                  onBack={() => setLoginMode('login')}
                  loading={loading}
                  error={error}
                  otpSent={otpSent}
                />
              )}

              {loginMode === 'forgot' && (
                <div className="space-y-6">
                  {forgotPasswordStep === 'method' && (
                    <ForgotPasswordForm
                      onSubmit={handleForgotPassword}
                      onBack={() => setLoginMode('login')}
                      loading={loading}
                      error={error}
                    />
                  )}
                  {forgotPasswordStep === 'otp' && (
                    <OTPForm
                      onSendOTP={(_phone, _method) => {}}
                      onVerifyOTP={handleOTPVerify}
                      onBack={() => setForgotPasswordStep('method')}
                      loading={loading}
                      error={error}
                      otpSent={true}
                      title="Enter Reset Code"
                      subtitle="We've sent a verification code to reset your password"
                    />
                  )}
                </div>
              )}

              {loginMode === 'access-request' && (
                <div className="space-y-6">
                  <AccessRequestForm
                    onSubmit={handleAccessRequest}
                    onBack={() => setLoginMode('login')}
                    loading={loading}
                    error={error}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Minimal Auth Navigation Tabs */}
          <div className="border-t border-slate-200 dark:border-slate-700">
            <AuthNavigationTabs
              currentMode={loginMode}
              onModeChange={setLoginMode}
              disabled={loading}
              className="bg-transparent border-0 shadow-none"
            />
          </div>
        </div>
      </div>

      {/* Dev Login Button - Only show in development */}
      {import.meta.env.DEV && (
        <button
          onClick={() => setShowDevLogin(true)}
          className="fixed top-6 left-6 z-50 px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-black font-medium rounded-lg shadow-lg transition-colors"
          title="Development Login Options"
        >
          🔧 Dev Login
        </button>
      )}

      {/* Dev Login Modal */}
      {showDevLogin && (
        <DevLoginModal
          isOpen={showDevLogin}
          onClose={() => setShowDevLogin(false)}
          onLogin={user => {
            setShowDevLogin(false);
            onLogin?.(user);
          }}
        />
      )}
    </div>
  );
}
